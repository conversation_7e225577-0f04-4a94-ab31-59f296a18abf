#include "run_modes.h"
#include "encoder.h"
#include "motor_control.h"
#include "pid_controller.h"
#include "display.h"
#include <Arduino.h>

// 全局模式数据实例
ModeData modeData;

// 初始化运行模式
void initRunModes() {
  resetModeData();
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("运行模式初始化完成");
  #endif
}

// 重置模式数据
void resetModeData() {
  modeData.currentMode = MODE_INDEPENDENT;
  modeData.currentStatus = STATUS_STOPPED;
  modeData.motor1TargetRPM = 0.0;
  modeData.motor2TargetRPM = 0.0;
  modeData.targetPhase = 0.0;
  modeData.modeStartTime = 0;
  modeData.syncStartTime = 0;
  modeData.syncAchieved = false;
  modeData.phaseAchieved = false;
  modeData.maxSpeedError = 0.0;
  modeData.maxPhaseError = 0.0;
}

// 更新运行模式
void updateRunModes() {
  // 如果处于急停状态，不更新模式
  if (isEmergencyStop()) {
    modeData.currentStatus = STATUS_EMERGENCY;
    return;
  }
  
  // 如果电机失步，设置错误状态
  if (isMotorStalled(1) || isMotorStalled(2)) {
    modeData.currentStatus = STATUS_ERROR;
    #if ENABLE_SERIAL_DEBUG
    Serial.println("电机失步检测!");
    #endif
    return;
  }
  
  // 根据当前模式调用相应的处理函数
  switch (modeData.currentMode) {
    case MODE_INDEPENDENT:
      processIndependentMode();
      break;
    case MODE_SYNC:
      processSyncMode();
      break;
    case MODE_PHASE_LOCK:
      processPhaseLockMode();
      break;
    case MODE_CHASE:
      processChaseMode();
      break;
    case MODE_MANUAL:
      processManualMode();
      break;
  }
  
  // 更新模式状态
  updateModeStatus();
  
  // 计算误差
  calculateErrors();
}

// 设置运行模式
void setRunMode(RunMode mode) {
  // 如果模式没有变化，直接返回
  if (mode == modeData.currentMode) return;
  
  // 停止当前模式
  stopMode();
  
  // 设置新模式
  modeData.currentMode = mode;
  
  // 根据模式设置PID控制器
  switch (mode) {
    case MODE_INDEPENDENT:
      enablePID(&speedPID1, true);
      enablePID(&speedPID2, true);
      enablePID(&phasePID, false);
      break;
    case MODE_SYNC:
      enablePID(&speedPID1, true);
      enablePID(&speedPID2, true);
      enablePID(&phasePID, false);
      break;
    case MODE_PHASE_LOCK:
      enablePID(&speedPID1, true);
      enablePID(&speedPID2, true);
      enablePID(&phasePID, true);
      break;
    case MODE_CHASE:
      enablePID(&speedPID1, true);
      enablePID(&speedPID2, true);
      enablePID(&phasePID, true);
      break;
    case MODE_MANUAL:
      enablePID(&speedPID1, false);
      enablePID(&speedPID2, false);
      enablePID(&phasePID, false);
      break;
  }
  
  // 重置模式数据
  modeData.modeStartTime = 0;
  modeData.syncStartTime = 0;
  modeData.syncAchieved = false;
  modeData.phaseAchieved = false;
  modeData.currentStatus = STATUS_STOPPED;
  
  #if ENABLE_SERIAL_DEBUG
  Serial.print("模式切换: ");
  Serial.println(mode);
  #endif
}

// 启动当前模式
void startMode() {
  if (modeData.currentStatus != STATUS_STOPPED) return;
  
  modeData.modeStartTime = millis();
  modeData.currentStatus = STATUS_STARTING;
  
  // 根据模式设置初始参数
  switch (modeData.currentMode) {
    case MODE_INDEPENDENT:
      setMotorSpeed(1, modeData.motor1TargetRPM);
      setMotorSpeed(2, modeData.motor2TargetRPM);
      break;
    case MODE_SYNC:
      setMotorSpeed(1, modeData.motor1TargetRPM);
      setMotorSpeed(2, modeData.motor1TargetRPM); // 同步模式下两个电机转速相同
      break;
    case MODE_PHASE_LOCK:
      setMotorSpeed(1, modeData.motor1TargetRPM);
      setMotorSpeed(2, modeData.motor1TargetRPM);
      setPhaseSetpoint(modeData.targetPhase);
      break;
    case MODE_CHASE:
      setMotorSpeed(1, modeData.motor1TargetRPM);
      setMotorSpeed(2, 0); // 追逐模式下从电机初始静止
      setPhaseSetpoint(0); // 目标相位差为0
      break;
    case MODE_MANUAL:
      // 手动模式下不自动设置速度
      break;
  }
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("模式启动");
  #endif
}

// 停止当前模式
void stopMode() {
  stopAllMotors();
  resetPID(&speedPID1);
  resetPID(&speedPID2);
  resetPID(&phasePID);
  modeData.currentStatus = STATUS_STOPPED;
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("模式停止");
  #endif
}

// 暂停当前模式
void pauseMode() {
  if (modeData.currentStatus != STATUS_RUNNING && 
      modeData.currentStatus != STATUS_SYNCING && 
      modeData.currentStatus != STATUS_CHASING) return;
  
  // 保存当前速度设定值
  modeData.motor1TargetRPM = getMotorTargetRPM(1);
  modeData.motor2TargetRPM = getMotorTargetRPM(2);
  
  // 停止电机
  stopAllMotors();
  
  modeData.currentStatus = STATUS_STOPPED;
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("模式暂停");
  #endif
}

// 恢复当前模式
void resumeMode() {
  if (modeData.currentStatus != STATUS_STOPPED) return;
  
  // 恢复之前的速度设定值
  setMotorSpeed(1, modeData.motor1TargetRPM);
  setMotorSpeed(2, modeData.motor2TargetRPM);
  
  modeData.modeStartTime = millis();
  modeData.currentStatus = STATUS_STARTING;
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("模式恢复");
  #endif
}

// 处理独立调速模式
void processIndependentMode() {
  // 独立调速模式下，两个电机独立控制
  if (modeData.currentStatus == STATUS_STARTING) {
    // 如果两个电机都达到目标转速，切换到运行状态
    if (abs(getMotorRPM(1) - modeData.motor1TargetRPM) <= RPM_ERROR_MAX &&
        abs(getMotorRPM(2) - modeData.motor2TargetRPM) <= RPM_ERROR_MAX) {
      modeData.currentStatus = STATUS_RUNNING;
    }
  }
}

// 处理同步运行模式
void processSyncMode() {
  // 同步运行模式下，两个电机保持相同转速
  if (modeData.currentStatus == STATUS_STARTING) {
    // 设置两个电机相同的目标转速
    setMotorSpeed(2, modeData.motor1TargetRPM);
    
    // 如果两个电机都达到目标转速，切换到同步状态
    if (abs(getMotorRPM(1) - modeData.motor1TargetRPM) <= RPM_ERROR_MAX &&
        abs(getMotorRPM(2) - modeData.motor1TargetRPM) <= RPM_ERROR_MAX) {
      modeData.currentStatus = STATUS_SYNCING;
      modeData.syncStartTime = millis();
    }
  } else if (modeData.currentStatus == STATUS_SYNCING) {
    // 检查是否已经同步
    if (isSpeedSynced()) {
      if (!modeData.syncAchieved) {
        modeData.syncAchieved = true;
        #if ENABLE_SERIAL_DEBUG
        Serial.println("速度同步已达成");
        #endif
      }
      modeData.currentStatus = STATUS_RUNNING;
    }
  }
}

// 处理相位锁定模式
void processPhaseLockMode() {
  // 相位锁定模式下，两个电机保持相同转速和固定相位差
  if (modeData.currentStatus == STATUS_STARTING) {
    // 设置两个电机相同的目标转速
    setMotorSpeed(2, modeData.motor1TargetRPM);
    
    // 如果两个电机都达到目标转速，切换到同步状态
    if (abs(getMotorRPM(1) - modeData.motor1TargetRPM) <= RPM_ERROR_MAX &&
        abs(getMotorRPM(2) - modeData.motor1TargetRPM) <= RPM_ERROR_MAX) {
      modeData.currentStatus = STATUS_SYNCING;
      modeData.syncStartTime = millis();
      
      // 启用相位PID控制器
      enablePID(&phasePID, true);
      setPhaseSetpoint(modeData.targetPhase);
    }
  } else if (modeData.currentStatus == STATUS_SYNCING) {
    // 检查是否已经同步和相位锁定
    if (isSpeedSynced() && isPhaseLocked()) {
      if (!modeData.syncAchieved) {
        modeData.syncAchieved = true;
        #if ENABLE_SERIAL_DEBUG
        Serial.println("速度同步和相位锁定已达成");
        #endif
      }
      modeData.currentStatus = STATUS_RUNNING;
    }
  }
}

// 处理追逐模式
void processChaseMode() {
  // 追逐模式下，主电机恒速运行，从电机从静止加速到与主电机同速同相
  if (modeData.currentStatus == STATUS_STARTING) {
    // 设置主电机目标转速
    setMotorSpeed(1, modeData.motor1TargetRPM);
    
    // 如果主电机达到目标转速，开始追逐过程
    if (abs(getMotorRPM(1) - modeData.motor1TargetRPM) <= RPM_ERROR_MAX) {
      modeData.currentStatus = STATUS_CHASING;
      modeData.syncStartTime = millis();
      
      // 启用相位PID控制器
      enablePID(&phasePID, true);
      setPhaseSetpoint(0); // 目标相位差为0
      
      // 计算从电机加速曲线
      float targetRPM = modeData.motor1TargetRPM;
      unsigned long chaseTime = CHASE_ACCEL_TIME;
      
      #if ENABLE_SERIAL_DEBUG
      Serial.println("开始追逐模式");
      #endif
    }
  } else if (modeData.currentStatus == STATUS_CHASING) {
    // 计算从电机当前应该的转速（平滑加速）
    unsigned long elapsedTime = millis() - modeData.syncStartTime;
    
    if (elapsedTime < CHASE_ACCEL_TIME) {
      // 使用平滑加速曲线（S形曲线）
      float progress = (float)elapsedTime / CHASE_ACCEL_TIME;
      float accelFactor = 0.5f - 0.5f * cos(PI * progress); // 0到1的S形曲线
      float targetRPM = modeData.motor1TargetRPM * accelFactor;
      
      // 设置从电机目标转速
      setMotorSpeed(2, targetRPM);
    } else {
      // 加速完成，设置与主电机相同的转速
      setMotorSpeed(2, modeData.motor1TargetRPM);
      
      // 检查是否已经同步和相位锁定
      if (isSpeedSynced() && isPhaseLocked()) {
        if (!modeData.syncAchieved) {
          modeData.syncAchieved = true;
          #if ENABLE_SERIAL_DEBUG
          Serial.println("追逐模式同步已达成");
          Serial.print("追逐时间: ");
          Serial.print(elapsedTime);
          Serial.println(" ms");
          #endif
        }
        modeData.currentStatus = STATUS_RUNNING;
      }
    }
  }
}

// 处理手动模式
void processManualMode() {
  // 手动模式下，直接使用设定的PWM值控制电机
  // 这个模式主要用于调试和测试
  modeData.currentStatus = STATUS_RUNNING;
}

// 检查速度是否同步
bool isSpeedSynced() {
  float speedError = abs(getMotorRPM(1) - getMotorRPM(2));
  return (speedError <= RPM_ERROR_MAX);
}

// 检查相位是否锁定
bool isPhaseLocked() {
  float phaseError = abs(getCurrentPhaseError() - modeData.targetPhase);
  while (phaseError > 180.0) phaseError -= 360.0;
  return (abs(phaseError) <= PHASE_ERROR_MAX);
}

// 检查系统是否稳定
bool isSystemStable() {
  return isSpeedSynced() && isPhaseLocked();
}

// 获取速度误差
float getSpeedError() {
  return abs(getMotorRPM(1) - getMotorRPM(2));
}

// 获取当前相位误差
float getCurrentPhaseError() {
  return getPhaseError();
}

// 计算误差
void calculateErrors() {
  float speedError = getSpeedError();
  float phaseError = abs(getCurrentPhaseError() - modeData.targetPhase);
  while (phaseError > 180.0) phaseError -= 360.0;
  phaseError = abs(phaseError);
  
  // 更新最大误差
  if (speedError > modeData.maxSpeedError) {
    modeData.maxSpeedError = speedError;
  }
  
  if (phaseError > modeData.maxPhaseError) {
    modeData.maxPhaseError = phaseError;
  }
}

// 更新模式状态
void updateModeStatus() {
  // 如果处于运行状态，检查是否仍然稳定
  if (modeData.currentStatus == STATUS_RUNNING) {
    if (modeData.currentMode == MODE_SYNC || modeData.currentMode == MODE_PHASE_LOCK) {
      if (!isSpeedSynced()) {
        modeData.currentStatus = STATUS_SYNCING;
        modeData.syncAchieved = false;
        #if ENABLE_SERIAL_DEBUG
        Serial.println("同步丢失，重新同步中");
        #endif
      }
    }
  }
}
