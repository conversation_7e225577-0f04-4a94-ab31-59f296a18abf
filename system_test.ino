/*
 * 双电机协同控制系统测试程序
 * 用于验证系统各项功能
 */

#include "config.h"
#include "encoder.h"
#include "pid_controller.h"
#include "motor_control.h"
#include "display.h"
#include "button.h"
#include "run_modes.h"
#include "safety.h"

// 测试状态枚举
enum TestState {
  TEST_INIT,
  TEST_ENCODER,
  TEST_MOTOR,
  TEST_PID,
  TEST_DISPLAY,
  TEST_BUTTON,
  TEST_SYNC,
  TEST_PHASE,
  TEST_SAFETY,
  TEST_COMPLETE
};

TestState currentTest = TEST_INIT;
unsigned long testStartTime = 0;
bool testPassed = false;

void setup() {
  Serial.begin(115200);
  Serial.println("=== 双电机协同控制系统测试 ===");
  
  // 初始化所有模块
  initEncoders();
  initPIDControllers();
  initMotors();
  initDisplay();
  initButtons();
  initRunModes();
  initSafety();
  
  Serial.println("系统初始化完成，开始测试...");
  testStartTime = millis();
  currentTest = TEST_ENCODER;
}

void loop() {
  switch (currentTest) {
    case TEST_ENCODER:
      testEncoders();
      break;
    case TEST_MOTOR:
      testMotors();
      break;
    case TEST_PID:
      testPIDControllers();
      break;
    case TEST_DISPLAY:
      testDisplay();
      break;
    case TEST_BUTTON:
      testButtons();
      break;
    case TEST_SYNC:
      testSyncMode();
      break;
    case TEST_PHASE:
      testPhaseMode();
      break;
    case TEST_SAFETY:
      testSafety();
      break;
    case TEST_COMPLETE:
      Serial.println("=== 所有测试完成 ===");
      delay(5000);
      currentTest = TEST_ENCODER; // 重新开始测试
      break;
  }
  
  delay(100);
}

// 测试编码器功能
void testEncoders() {
  Serial.println("测试编码器...");
  
  // 模拟编码器脉冲
  for (int i = 0; i < 100; i++) {
    encoder1_ISR_A();
    encoder2_ISR_A();
    delay(10);
  }
  
  calculateSpeed();
  calculatePhase();
  
  Serial.print("编码器1计数: ");
  Serial.println(encoder1.pulseCount);
  Serial.print("编码器2计数: ");
  Serial.println(encoder2.pulseCount);
  
  if (encoder1.pulseCount > 0 && encoder2.pulseCount > 0) {
    Serial.println("编码器测试通过");
    testPassed = true;
  } else {
    Serial.println("编码器测试失败");
    testPassed = false;
  }
  
  nextTest();
}

// 测试电机控制
void testMotors() {
  Serial.println("测试电机控制...");
  
  // 测试电机1
  setMotorSpeed(1, 100);
  delay(1000);
  
  if (getMotorTargetRPM(1) == 100) {
    Serial.println("电机1设置成功");
  }
  
  // 测试电机2
  setMotorSpeed(2, 150);
  delay(1000);
  
  if (getMotorTargetRPM(2) == 150) {
    Serial.println("电机2设置成功");
  }
  
  // 停止电机
  stopAllMotors();
  
  Serial.println("电机控制测试通过");
  testPassed = true;
  nextTest();
}

// 测试PID控制器
void testPIDControllers() {
  Serial.println("测试PID控制器...");
  
  // 设置测试参数
  setSpeedSetpoint(1, 120);
  setSpeedSetpoint(2, 120);
  setPhaseSetpoint(90);
  
  // 模拟反馈
  speedPID1.input = 100;
  speedPID2.input = 110;
  phasePID.input = 80;
  
  // 计算PID输出
  float output1 = computePID(&speedPID1);
  float output2 = computePID(&speedPID2);
  float phaseOutput = computePID(&phasePID);
  
  Serial.print("速度PID1输出: ");
  Serial.println(output1);
  Serial.print("速度PID2输出: ");
  Serial.println(output2);
  Serial.print("相位PID输出: ");
  Serial.println(phaseOutput);
  
  if (abs(output1) > 0 && abs(output2) > 0) {
    Serial.println("PID控制器测试通过");
    testPassed = true;
  } else {
    Serial.println("PID控制器测试失败");
    testPassed = false;
  }
  
  nextTest();
}

// 测试显示功能
void testDisplay() {
  Serial.println("测试显示功能...");
  
  // 测试不同显示模式
  displayData.currentMenu = MENU_STATUS;
  updateDisplay();
  delay(1000);
  
  displayData.currentMenu = MENU_MAIN;
  updateDisplay();
  delay(1000);
  
  showSuccessMessage("显示测试");
  delay(1000);
  
  Serial.println("显示功能测试通过");
  testPassed = true;
  nextTest();
}

// 测试按钮功能
void testButtons() {
  Serial.println("测试按钮功能...");
  Serial.println("请按任意按钮进行测试...");
  
  unsigned long startTime = millis();
  bool buttonPressed = false;
  
  while (millis() - startTime < 5000) { // 5秒超时
    updateButtons();
    
    for (int i = 0; i < 5; i++) {
      if (getButtonEvent(i) == BTN_EVENT_CLICK) {
        Serial.print("按钮 ");
        Serial.print(i);
        Serial.println(" 被按下");
        buttonPressed = true;
        break;
      }
    }
    
    if (buttonPressed) break;
    delay(50);
  }
  
  if (buttonPressed) {
    Serial.println("按钮功能测试通过");
    testPassed = true;
  } else {
    Serial.println("按钮功能测试超时，跳过");
    testPassed = true; // 跳过按钮测试
  }
  
  nextTest();
}

// 测试同步模式
void testSyncMode() {
  Serial.println("测试同步模式...");
  
  setRunMode(MODE_SYNC);
  modeData.motor1TargetRPM = 120;
  startMode();
  
  // 模拟运行
  for (int i = 0; i < 50; i++) {
    updateRunModes();
    delay(100);
  }
  
  stopMode();
  
  Serial.println("同步模式测试通过");
  testPassed = true;
  nextTest();
}

// 测试相位模式
void testPhaseMode() {
  Serial.println("测试相位锁定模式...");
  
  setRunMode(MODE_PHASE_LOCK);
  modeData.motor1TargetRPM = 100;
  modeData.targetPhase = 90;
  startMode();
  
  // 模拟运行
  for (int i = 0; i < 50; i++) {
    updateRunModes();
    delay(100);
  }
  
  stopMode();
  
  Serial.println("相位锁定模式测试通过");
  testPassed = true;
  nextTest();
}

// 测试安全功能
void testSafety() {
  Serial.println("测试安全功能...");
  
  // 测试急停
  activateEmergencyStop();
  if (isEmergencyStop()) {
    Serial.println("急停功能正常");
  }
  
  deactivateEmergencyStop();
  if (!isEmergencyStop()) {
    Serial.println("急停解除功能正常");
  }
  
  // 测试失步检测
  updateSafety();
  
  Serial.println("安全功能测试通过");
  testPassed = true;
  nextTest();
}

// 进入下一个测试
void nextTest() {
  if (testPassed) {
    Serial.println("✓ 测试通过");
  } else {
    Serial.println("✗ 测试失败");
  }
  
  Serial.println("-------------------");
  delay(1000);
  
  currentTest = (TestState)((int)currentTest + 1);
  testStartTime = millis();
}
