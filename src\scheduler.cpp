/**
 * @file scheduler.cpp
 * @brief 简单的时间轮询调度器实现
 */

#include "scheduler.h"
#include "oled.h"

// 全局任务数量
uint8_t task_num = 0;

// 静态任务数组，每个任务包含函数指针、执行周期、上次执行时间
static task_t scheduler_tasks[] = {
    {oled_task, 500, 0, true}, // OLED更新任务，500ms执行一次
    {uart_task, 1000, 0, true} // 串口调试任务，1000ms执行一次
};

/**
 * @brief 调度器初始化函数
 * 计算任务数组中的任务数量并存储到 task_num 中
 */
void scheduler_init(void)
{
    // 计算任务数组中的任务数量并存储到 task_num 中
    task_num = sizeof(scheduler_tasks) / sizeof(task_t);

    Serial.println("Scheduler initialized");
    Serial.print("Task count: ");
    Serial.println(task_num);
}

/**
 * @brief 调度器运行函数
 * 遍历所有任务，检查是否到达执行时间，如果到达则执行任务并更新上次执行时间
 */
void scheduler_run(void)
{
    // 遍历所有任务进行调度
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 检查任务是否启用
        if (!scheduler_tasks[i].enabled)
        {
            continue;
        }

        // 获取当前系统时间（毫秒）
        uint32_t now_time = millis();

        // 检查当前时间是否到达任务执行时间
        if (now_time >= scheduler_tasks[i].rate_ms + scheduler_tasks[i].last_run)
        {
            // 更新任务上次执行时间为当前时间
            scheduler_tasks[i].last_run = now_time;

            // 执行任务函数
            scheduler_tasks[i].task_func();
        }
    }
}

/**
 * @brief 动态添加任务
 * @param func 任务函数指针
 * @param rate_ms 执行周期(毫秒)
 */
void scheduler_add_task(task_func_t func, uint32_t rate_ms)
{
    if (task_num < sizeof(scheduler_tasks) / sizeof(task_t))
    {
        scheduler_tasks[task_num].task_func = func;
        scheduler_tasks[task_num].rate_ms = rate_ms;
        scheduler_tasks[task_num].last_run = 0;
        scheduler_tasks[task_num].enabled = true;
        task_num++;

        Serial.print("Task added, period: ");
        Serial.print(rate_ms);
        Serial.println("ms");
    }
    else
    {
        Serial.println("Task array full, cannot add new task");
    }
}

/**
 * @brief 启用/禁用任务
 * @param task_id 任务ID
 * @param enable 是否启用
 */
void scheduler_enable_task(uint8_t task_id, bool enable)
{
    if (task_id < task_num)
    {
        scheduler_tasks[task_id].enabled = enable;
        Serial.print("Task ");
        Serial.print(task_id);
        Serial.print(enable ? " enabled" : " disabled");
        Serial.println();
    }
}

// ==================== 任务函数实现 ====================

// 全局计数器
static uint32_t oled_counter = 0;
static uint32_t uart_counter = 0;

/**
 * @brief OLED显示任务
 */
void oled_task(void)
{
    oled_counter++;

    // 更新OLED显示
    oled_show_status(oled_counter, millis());
}

/**
 * @brief 串口调试任务
 */
void uart_task(void)
{
    uart_counter++;

    // Output debug info
    Serial.print("System running - OLED count: ");
    Serial.print(oled_counter);
    Serial.print(", UART count: ");
    Serial.print(uart_counter);
    Serial.print(", Runtime: ");
    Serial.print(millis() / 1000);
    Serial.println("s");
}
