/**
 * @file oled.cpp
 * @brief OLED显示模块实现 - 支持中文显示
 */

#include "oled.h"

// U8g2 OLED显示对象 - 支持中文字体
U8G2_SSD1306_128X64_NONAME_F_HW_I2C u8g2(U8G2_R0, /* reset=*/U8X8_PIN_NONE);

/**
 * @brief 初始化OLED显示
 * @return true 初始化成功, false 初始化失败
 */
bool oled_init(void)
{
    // 初始化U8g2显示屏
    if (!u8g2.begin())
    {
        Serial.println("OLED init failed!");
        return false;
    }

    Serial.println("OLED init success!");

    // 设置中文字体
    u8g2.setFont(u8g2_font_wqy12_t_chinese1);

    // 强制清屏
    u8g2.clearBuffer();
    u8g2.sendBuffer();
    delay(100);

    // 显示测试信息确认清屏成功
    u8g2.clearBuffer();
    u8g2.setCursor(0, 15);
    u8g2.print("屏幕已清除!");
    u8g2.setCursor(0, 30);
    u8g2.print("正在初始化...");
    u8g2.sendBuffer();
    delay(1000);

    return true;
}

/**
 * @brief 显示启动画面
 */
void oled_show_splash(void)
{
    u8g2.clearBuffer();

    // 设置大字体显示标题
    u8g2.setFont(u8g2_font_wqy16_t_chinese1);
    u8g2.setCursor(10, 20);
    u8g2.print("FreeRTOS");

    // 设置小字体显示详细信息
    u8g2.setFont(u8g2_font_wqy12_t_chinese1);
    u8g2.setCursor(0, 35);
    u8g2.print("OLED测试系统");
    u8g2.setCursor(0, 48);
    u8g2.print("Arduino Mega 2560");
    u8g2.setCursor(0, 61);
    u8g2.print("系统启动中...");

    u8g2.sendBuffer();
}

/**
 * @brief 显示系统状态
 * @param counter 计数器值
 * @param runtime 运行时间(毫秒)
 */
void oled_show_status(uint32_t counter, uint32_t runtime)
{
    u8g2.clearBuffer();
    u8g2.setFont(u8g2_font_wqy12_t_chinese1);

    // 标题
    u8g2.setCursor(0, 12);
    u8g2.print("=== 系统运行中 ===");

    // 计数器
    u8g2.setCursor(0, 26);
    u8g2.print("计数: ");
    u8g2.print(counter);

    // 运行时间
    u8g2.setCursor(0, 40);
    u8g2.print("时间: ");
    u8g2.print(runtime / 1000);
    u8g2.print("s");

    // 系统状态
    u8g2.setCursor(0, 54);
    u8g2.print("状态: 正常  OLED: 工作");

    // 进度条
    int progress = (counter % 20) * 6;
    u8g2.drawFrame(0, 58, 120, 6);
    if (progress > 0)
    {
        u8g2.drawBox(1, 59, progress, 4);
    }

    // 动画字符
    u8g2.setCursor(110, 54);
    char anim[] = "|/-\\";
    u8g2.print(anim[counter % 4]);

    u8g2.sendBuffer();
}