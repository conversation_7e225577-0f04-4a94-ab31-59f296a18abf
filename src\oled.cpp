/**
 * @file oled.cpp
 * @brief OLED显示模块实现 - 支持中文显示
 */

#include "oled.h"

// Adafruit OLED显示对象
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

/**
 * @brief 初始化OLED显示
 * @return true 初始化成功, false 初始化失败
 */
bool oled_init(void)
{
    // 初始化OLED显示屏
    if (!display.begin(SSD1306_SWITCHCAPVCC, OLED_ADDRESS))
    {
        Serial.println("OLED init failed!");
        return false;
    }

    Serial.println("OLED init success!");

    // 强制清屏 - 多次清除确保没有残留
    for (int i = 0; i < 3; i++)
    {
        display.clearDisplay();
        display.fillScreen(SSD1306_BLACK); // 填充黑色
        display.display();
        delay(100);
    }

    // 设置基本参数
    display.setTextColor(SSD1306_WHITE);
    display.setTextSize(1);

    // 显示测试信息确认清屏成功
    display.clearDisplay();
    display.setCursor(0, 0);
    display.println("Screen Cleared!");
    display.println("Initializing...");
    display.display();
    delay(1000);

    return true;
}

/**
 * @brief 显示启动画面
 */
void oled_show_splash(void)
{
    display.clearDisplay();
    display.setTextSize(2);
    display.setCursor(0, 0);
    display.println("FreeRTOS");

    display.setTextSize(1);
    display.println("");
    display.println("OLED Test System");
    display.println("Arduino Mega 2560");
    display.println("");
    display.println("System Starting...");
    display.display();
}

/**
 * @brief 显示系统状态
 * @param counter 计数器值
 * @param runtime 运行时间(毫秒)
 */
void oled_show_status(uint32_t counter, uint32_t runtime)
{
    display.clearDisplay();
    display.setTextSize(1);
    display.setCursor(0, 0);

    // Title
    display.println("=== System Running ===");
    display.println("");

    // Counter
    display.print("Counter: ");
    display.println(counter);

    // Runtime
    display.print("Runtime: ");
    display.print(runtime / 1000);
    display.println("s");

    // System status
    display.println("Status: OK");
    display.println("OLED: Working");

    // Progress bar
    int progress = (counter % 20) * 6;
    display.drawRect(0, 50, 120, 8, SSD1306_WHITE);
    if (progress > 0)
    {
        display.fillRect(2, 52, progress, 4, SSD1306_WHITE);
    }

    // Animation character
    display.setCursor(110, 50);
    char anim[] = "|/-\\";
    display.print(anim[counter % 4]);

    display.display();
}