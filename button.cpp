#include "button.h"
#include "display.h"
#include "motor_control.h"
#include <Arduino.h>

// 全局按钮数据实例
ButtonData buttons[5];

// 按钮引脚映射
const int buttonPins[5] = {
  BTN_MODE_PIN,   // 模式切换按钮
  BTN_SET_PIN,    // 设置按钮
  BTN_UP_PIN,     // 增加按钮
  BTN_DOWN_PIN,   // 减少按钮
  BTN_OK_PIN      // 确认按钮
};

// 初始化按钮
void initButtons() {
  for (int i = 0; i < 5; i++) {
    buttons[i].pin = buttonPins[i];
    buttons[i].state = BTN_RELEASED;
    buttons[i].lastState = BTN_RELEASED;
    buttons[i].pressTime = 0;
    buttons[i].releaseTime = 0;
    buttons[i].lastDebounceTime = 0;
    buttons[i].lastReading = HIGH; // 上拉电阻，默认高电平
    buttons[i].currentReading = HIGH;
    buttons[i].longPressTriggered = false;
    buttons[i].lastRepeatTime = 0;
    
    // 设置引脚为输入模式，启用内部上拉电阻
    pinMode(buttons[i].pin, INPUT_PULLUP);
  }
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("按钮初始化完成");
  #endif
}

// 更新按钮状态
void updateButtons() {
  for (int i = 0; i < 5; i++) {
    debounceButton(i);
    processButtonState(i);
  }
}

// 获取按钮事件
ButtonEvent getButtonEvent(int buttonIndex) {
  if (buttonIndex < 0 || buttonIndex >= 5) return BTN_EVENT_NONE;
  
  ButtonData* btn = &buttons[buttonIndex];
  ButtonEvent event = BTN_EVENT_NONE;
  
  // 检查单击事件
  if (btn->state == BTN_RELEASED && btn->lastState == BTN_PRESSED && 
      !btn->longPressTriggered) {
    event = BTN_EVENT_CLICK;
  }
  
  // 检查长按事件
  if (btn->state == BTN_LONG_PRESS && !btn->longPressTriggered) {
    btn->longPressTriggered = true;
    event = BTN_EVENT_LONG_CLICK;
  }
  
  // 检查重复事件
  if (btn->state == BTN_REPEAT) {
    event = BTN_EVENT_REPEAT;
  }
  
  return event;
}

// 检查按钮是否按下
bool isButtonPressed(int buttonIndex) {
  if (buttonIndex < 0 || buttonIndex >= 5) return false;
  return (buttons[buttonIndex].state == BTN_PRESSED || 
          buttons[buttonIndex].state == BTN_LONG_PRESS ||
          buttons[buttonIndex].state == BTN_REPEAT);
}

// 检查按钮是否长按
bool isButtonLongPressed(int buttonIndex) {
  if (buttonIndex < 0 || buttonIndex >= 5) return false;
  return (buttons[buttonIndex].state == BTN_LONG_PRESS ||
          buttons[buttonIndex].state == BTN_REPEAT);
}

// 重置按钮状态
void resetButton(int buttonIndex) {
  if (buttonIndex < 0 || buttonIndex >= 5) return;
  
  ButtonData* btn = &buttons[buttonIndex];
  btn->state = BTN_RELEASED;
  btn->lastState = BTN_RELEASED;
  btn->longPressTriggered = false;
  btn->lastRepeatTime = 0;
}

// 处理按钮事件
void handleButtonEvents() {
  // 模式切换按钮
  ButtonEvent modeEvent = getButtonEvent(BTN_MODE_INDEX);
  if (modeEvent == BTN_EVENT_CLICK) {
    displayData.currentMenu = MENU_MAIN;
    displayData.selectedItem = 0;
    refreshDisplay();
  }
  
  // 设置按钮
  ButtonEvent setEvent = getButtonEvent(BTN_SET_INDEX);
  if (setEvent == BTN_EVENT_CLICK) {
    selectMenuItem();
  } else if (setEvent == BTN_EVENT_LONG_CLICK) {
    exitEditMode();
  }
  
  // 上按钮
  ButtonEvent upEvent = getButtonEvent(BTN_UP_INDEX);
  if (upEvent == BTN_EVENT_CLICK || upEvent == BTN_EVENT_REPEAT) {
    navigateMenu(-1);
  }
  
  // 下按钮
  ButtonEvent downEvent = getButtonEvent(BTN_DOWN_INDEX);
  if (downEvent == BTN_EVENT_CLICK || downEvent == BTN_EVENT_REPEAT) {
    navigateMenu(1);
  }
  
  // 确认按钮
  ButtonEvent okEvent = getButtonEvent(BTN_OK_INDEX);
  if (okEvent == BTN_EVENT_CLICK) {
    selectMenuItem();
  } else if (okEvent == BTN_EVENT_LONG_CLICK) {
    // 长按确认键触发急停
    if (isEmergencyStop()) {
      releaseEmergencyStop();
      showSuccessMessage("急停已解除");
    } else {
      emergencyStop();
      showErrorMessage("急停激活");
    }
  }
}

// 读取按钮状态
bool readButton(int buttonIndex) {
  if (buttonIndex < 0 || buttonIndex >= 5) return HIGH;
  return digitalRead(buttons[buttonIndex].pin);
}

// 按钮防抖
void debounceButton(int buttonIndex) {
  if (buttonIndex < 0 || buttonIndex >= 5) return;
  
  ButtonData* btn = &buttons[buttonIndex];
  bool reading = readButton(buttonIndex);
  
  // 如果读取值发生变化，重置防抖计时器
  if (reading != btn->lastReading) {
    btn->lastDebounceTime = millis();
  }
  
  // 如果读取值稳定超过防抖时间，更新当前读取值
  if ((millis() - btn->lastDebounceTime) > BTN_DEBOUNCE_TIME) {
    if (reading != btn->currentReading) {
      btn->currentReading = reading;
      
      // 记录按下和释放时间
      if (btn->currentReading == LOW) { // 按下（上拉电阻，按下为低电平）
        btn->pressTime = millis();
        btn->longPressTriggered = false;
      } else { // 释放
        btn->releaseTime = millis();
      }
    }
  }
  
  btn->lastReading = reading;
}

// 处理按钮状态
void processButtonState(int buttonIndex) {
  if (buttonIndex < 0 || buttonIndex >= 5) return;
  
  ButtonData* btn = &buttons[buttonIndex];
  btn->lastState = btn->state;
  
  unsigned long currentTime = millis();
  
  if (btn->currentReading == LOW) { // 按钮按下
    unsigned long pressDuration = currentTime - btn->pressTime;
    
    if (pressDuration >= BTN_LONG_PRESS) {
      // 长按状态
      if (btn->state != BTN_LONG_PRESS && btn->state != BTN_REPEAT) {
        btn->state = BTN_LONG_PRESS;
        btn->lastRepeatTime = currentTime;
      } else if (btn->state == BTN_LONG_PRESS || btn->state == BTN_REPEAT) {
        // 检查是否需要重复触发
        if (currentTime - btn->lastRepeatTime >= BTN_REPEAT_RATE) {
          btn->state = BTN_REPEAT;
          btn->lastRepeatTime = currentTime;
        }
      }
    } else {
      // 普通按下状态
      btn->state = BTN_PRESSED;
    }
  } else { // 按钮释放
    btn->state = BTN_RELEASED;
  }
}
