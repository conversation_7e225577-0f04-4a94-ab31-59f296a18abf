/*
 * 最简单的OLED测试程序 - 支持中文显示
 * 直接在Arduino IDE中使用，不依赖任何其他文件
 */

#include <Wire.h>
#include <U8g2lib.h>

// OLED显示屏参数
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_ADDRESS 0x3C

// 创建U8g2显示对象 - 支持中文字体
U8G2_SSD1306_128X64_NONAME_F_HW_I2C u8g2(U8G2_R0, /* reset=*/U8X8_PIN_NONE);

// 测试变量
int counter = 0;
unsigned long lastUpdate = 0;
bool oledWorking = false;

void setup()
{
  Serial.begin(115200);
  Serial.println("=== 最简单OLED测试 ===");
  Serial.println("正在初始化...");

  // 初始化I2C
  Wire.begin();
  Serial.println("I2C初始化完成");

  // 尝试初始化OLED显示屏
  Serial.println("正在初始化OLED...");
  if (!u8g2.begin())
  {
    Serial.println("❌ OLED初始化失败!");
    Serial.println("请检查以下连接:");
    Serial.println("OLED VCC -> Arduino 5V");
    Serial.println("OLED GND -> Arduino GND");
    Serial.println("OLED SDA -> Arduino Pin 20");
    Serial.println("OLED SCL -> Arduino Pin 21");
    Serial.println("");
    Serial.println("如果连接正确，可能是I2C地址问题");
    Serial.println("尝试扫描I2C设备...");

    // I2C设备扫描
    scanI2CDevices();

    while (1)
    {
      delay(1000);
    }
  }

  Serial.println("✅ OLED初始化成功!");
  oledWorking = true;

  // 设置中文字体
  u8g2.setFont(u8g2_font_wqy12_t_chinese1);

  // 显示启动信息
  u8g2.clearBuffer();
  u8g2.setFont(u8g2_font_wqy16_t_chinese1);
  u8g2.setCursor(20, 20);
  u8g2.print("OLED");
  u8g2.setCursor(20, 40);
  u8g2.print("测试");
  u8g2.setFont(u8g2_font_wqy12_t_chinese1);
  u8g2.setCursor(0, 60);
  u8g2.print("初始化成功!");
  u8g2.sendBuffer();
  delay(2000);

  Serial.println("开始测试循环...");
}

void loop()
{
  unsigned long currentTime = millis();

  // 每秒更新一次显示
  if (currentTime - lastUpdate >= 1000)
  {
    counter++;
    lastUpdate = currentTime;

    if (oledWorking)
    {
      updateOLEDDisplay(currentTime);
    }

    // 串口输出
    Serial.print("✅ 系统正常运行 - 计数: ");
    Serial.print(counter);
    Serial.print(", 时间: ");
    Serial.print(currentTime / 1000);
    Serial.println("s");
  }

  delay(10);
}

void updateOLEDDisplay(unsigned long currentTime)
{
  // 清空显示缓冲区
  u8g2.clearBuffer();
  u8g2.setFont(u8g2_font_wqy12_t_chinese1);

  // 显示标题
  u8g2.setCursor(0, 12);
  u8g2.print("=== OLED测试 ===");

  // 显示计数器
  u8g2.setCursor(0, 26);
  u8g2.print("计数: ");
  u8g2.setFont(u8g2_font_wqy16_t_chinese1);
  u8g2.print(counter);

  // 显示运行时间
  u8g2.setFont(u8g2_font_wqy12_t_chinese1);
  u8g2.setCursor(0, 42);
  u8g2.print("时间: ");
  u8g2.print(currentTime / 1000);
  u8g2.print("s");

  // 显示状态
  u8g2.setCursor(0, 56);
  u8g2.print("状态: 正常");

  // 显示动态进度条
  int progress = (counter % 20) * 6;
  u8g2.drawFrame(0, 58, 120, 6);
  if (progress > 0)
  {
    u8g2.drawBox(1, 59, progress, 4);
  }

  // 显示小动画
  int animFrame = (counter % 4);
  u8g2.setCursor(110, 56);
  switch (animFrame)
  {
  case 0:
    u8g2.print("|");
    break;
  case 1:
    u8g2.print("/");
    break;
  case 2:
    u8g2.print("-");
    break;
  case 3:
    u8g2.print("\\");
    break;
  }

  // 更新显示
  u8g2.sendBuffer();
}

void scanI2CDevices()
{
  Serial.println("开始扫描I2C设备...");
  int deviceCount = 0;

  for (byte address = 1; address < 127; address++)
  {
    Wire.beginTransmission(address);
    byte error = Wire.endTransmission();

    if (error == 0)
    {
      Serial.print("发现I2C设备，地址: 0x");
      if (address < 16)
        Serial.print("0");
      Serial.println(address, HEX);
      deviceCount++;
    }
  }

  if (deviceCount == 0)
  {
    Serial.println("❌ 未发现任何I2C设备!");
    Serial.println("请检查连接线是否正确");
  }
  else
  {
    Serial.print("✅ 总共发现 ");
    Serial.print(deviceCount);
    Serial.println(" 个I2C设备");
    Serial.println("如果地址不是0x3C，请修改代码中的OLED_ADDRESS");
  }
}
