/*
 * 最简单的OLED测试程序
 * 直接在Arduino IDE中使用，不依赖任何其他文件
 */

#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

// OLED显示屏参数
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET -1
#define OLED_ADDRESS 0x3C

// 创建显示对象
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

// 测试变量
int counter = 0;
unsigned long lastUpdate = 0;
bool oledWorking = false;

void setup() {
  Serial.begin(115200);
  Serial.println("=== 最简单OLED测试 ===");
  Serial.println("正在初始化...");
  
  // 初始化I2C
  Wire.begin();
  Serial.println("I2C初始化完成");
  
  // 尝试初始化OLED显示屏
  Serial.println("正在初始化OLED...");
  if (!display.begin(SSD1306_SWITCHCAPVCC, OLED_ADDRESS)) {
    Serial.println("❌ OLED初始化失败!");
    Serial.println("请检查以下连接:");
    Serial.println("OLED VCC -> Arduino 5V");
    Serial.println("OLED GND -> Arduino GND");
    Serial.println("OLED SDA -> Arduino Pin 20");
    Serial.println("OLED SCL -> Arduino Pin 21");
    Serial.println("");
    Serial.println("如果连接正确，可能是I2C地址问题");
    Serial.println("尝试扫描I2C设备...");
    
    // I2C设备扫描
    scanI2CDevices();
    
    while(1) {
      delay(1000);
    }
  }
  
  Serial.println("✅ OLED初始化成功!");
  oledWorking = true;
  
  // 显示启动信息
  display.clearDisplay();
  display.setTextSize(2);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(0, 0);
  display.println("OLED");
  display.println("测试");
  display.setTextSize(1);
  display.println("");
  display.println("初始化成功!");
  display.display();
  delay(2000);
  
  Serial.println("开始测试循环...");
}

void loop() {
  unsigned long currentTime = millis();
  
  // 每秒更新一次显示
  if (currentTime - lastUpdate >= 1000) {
    counter++;
    lastUpdate = currentTime;
    
    if (oledWorking) {
      updateOLEDDisplay(currentTime);
    }
    
    // 串口输出
    Serial.print("✅ 系统正常运行 - 计数: ");
    Serial.print(counter);
    Serial.print(", 时间: ");
    Serial.print(currentTime / 1000);
    Serial.println("s");
  }
  
  delay(10);
}

void updateOLEDDisplay(unsigned long currentTime) {
  // 清空显示
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(0, 0);
  
  // 显示标题
  display.setTextSize(1);
  display.println("=== OLED测试 ===");
  display.println("");
  
  // 显示计数器
  display.setTextSize(1);
  display.print("计数: ");
  display.setTextSize(2);
  display.println(counter);
  
  display.setTextSize(1);
  display.println("");
  
  // 显示运行时间
  display.print("时间: ");
  display.print(currentTime / 1000);
  display.println("s");
  
  // 显示状态
  display.println("状态: 正常");
  
  // 显示动态进度条
  int progress = (counter % 20) * 6;
  display.drawRect(0, 50, 120, 8, SSD1306_WHITE);
  if (progress > 0) {
    display.fillRect(2, 52, progress, 4, SSD1306_WHITE);
  }
  
  // 显示小动画
  int animFrame = (counter % 4);
  display.setCursor(110, 50);
  switch(animFrame) {
    case 0: display.print("|"); break;
    case 1: display.print("/"); break;
    case 2: display.print("-"); break;
    case 3: display.print("\\"); break;
  }
  
  // 更新显示
  display.display();
}

void scanI2CDevices() {
  Serial.println("开始扫描I2C设备...");
  int deviceCount = 0;
  
  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    byte error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.print("发现I2C设备，地址: 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
      deviceCount++;
    }
  }
  
  if (deviceCount == 0) {
    Serial.println("❌ 未发现任何I2C设备!");
    Serial.println("请检查连接线是否正确");
  } else {
    Serial.print("✅ 总共发现 ");
    Serial.print(deviceCount);
    Serial.println(" 个I2C设备");
    Serial.println("如果地址不是0x3C，请修改代码中的OLED_ADDRESS");
  }
}
