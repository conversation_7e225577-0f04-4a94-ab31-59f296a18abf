#include "pid_controller.h"
#include "encoder.h"
#include <Arduino.h>

// 全局PID控制器实例
PIDController speedPID1;
PIDController speedPID2;
PIDController phasePID;

// 初始化PID控制器
void initPIDControllers() {
  // 初始化电机1速度环PID
  speedPID1.kp = SPEED_PID1_KP;
  speedPID1.ki = SPEED_PID1_KI;
  speedPID1.kd = SPEED_PID1_KD;
  speedPID1.setpoint = 0.0;
  speedPID1.input = 0.0;
  speedPID1.output = 0.0;
  speedPID1.lastInput = 0.0;
  speedPID1.integral = 0.0;
  speedPID1.lastError = 0.0;
  speedPID1.outputMin = PID_OUTPUT_MIN;
  speedPID1.outputMax = PID_OUTPUT_MAX;
  speedPID1.lastTime = millis();
  speedPID1.enabled = true;
  
  // 初始化电机2速度环PID
  speedPID2.kp = SPEED_PID2_KP;
  speedPID2.ki = SPEED_PID2_KI;
  speedPID2.kd = SPEED_PID2_KD;
  speedPID2.setpoint = 0.0;
  speedPID2.input = 0.0;
  speedPID2.output = 0.0;
  speedPID2.lastInput = 0.0;
  speedPID2.integral = 0.0;
  speedPID2.lastError = 0.0;
  speedPID2.outputMin = PID_OUTPUT_MIN;
  speedPID2.outputMax = PID_OUTPUT_MAX;
  speedPID2.lastTime = millis();
  speedPID2.enabled = true;
  
  // 初始化相位环PID
  phasePID.kp = PHASE_PID_KP;
  phasePID.ki = PHASE_PID_KI;
  phasePID.kd = PHASE_PID_KD;
  phasePID.setpoint = 0.0;
  phasePID.input = 0.0;
  phasePID.output = 0.0;
  phasePID.lastInput = 0.0;
  phasePID.integral = 0.0;
  phasePID.lastError = 0.0;
  phasePID.outputMin = -50.0; // 相位环输出限制较小
  phasePID.outputMax = 50.0;
  phasePID.lastTime = millis();
  phasePID.enabled = false; // 默认禁用，同步模式时启用
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("PID控制器初始化完成");
  #endif
}

// 计算PID输出
float computePID(PIDController* pid) {
  if (!pid->enabled) return 0.0;
  
  unsigned long now = millis();
  float timeChange = (now - pid->lastTime) / 1000.0; // 转换为秒
  
  if (timeChange >= 0.01) { // 最小计算间隔10ms
    // 计算误差
    float error = pid->setpoint - pid->input;
    
    // 积分项计算
    pid->integral += error * timeChange;
    
    // 积分限幅，防止积分饱和
    if (pid->integral > PID_INTEGRAL_MAX) pid->integral = PID_INTEGRAL_MAX;
    else if (pid->integral < -PID_INTEGRAL_MAX) pid->integral = -PID_INTEGRAL_MAX;
    
    // 微分项计算
    float derivative = (error - pid->lastError) / timeChange;
    
    // PID输出计算
    pid->output = pid->kp * error + pid->ki * pid->integral + pid->kd * derivative;
    
    // 输出限幅
    if (pid->output > pid->outputMax) pid->output = pid->outputMax;
    else if (pid->output < pid->outputMin) pid->output = pid->outputMin;
    
    // 保存当前值供下次计算使用
    pid->lastError = error;
    pid->lastInput = pid->input;
    pid->lastTime = now;
  }
  
  return pid->output;
}

// 设置PID参数
void setPIDTunings(PIDController* pid, float kp, float ki, float kd) {
  if (kp < 0 || ki < 0 || kd < 0) return; // 参数不能为负
  
  pid->kp = kp;
  pid->ki = ki;
  pid->kd = kd;
  
  #if ENABLE_SERIAL_DEBUG
  Serial.print("PID参数更新: Kp=");
  Serial.print(kp);
  Serial.print(", Ki=");
  Serial.print(ki);
  Serial.print(", Kd=");
  Serial.println(kd);
  #endif
}

// 设置输出限制
void setPIDLimits(PIDController* pid, float min, float max) {
  if (min >= max) return; // 最小值不能大于等于最大值
  
  pid->outputMin = min;
  pid->outputMax = max;
  
  // 如果当前输出超出新限制，则限制输出
  if (pid->output > max) pid->output = max;
  else if (pid->output < min) pid->output = min;
  
  // 限制积分项，防止积分饱和
  if (pid->integral > max) pid->integral = max;
  else if (pid->integral < min) pid->integral = min;
}

// 重置PID控制器
void resetPID(PIDController* pid) {
  pid->integral = 0.0;
  pid->lastError = 0.0;
  pid->lastInput = pid->input;
  pid->output = 0.0;
  pid->lastTime = millis();
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("PID控制器已重置");
  #endif
}

// 使能/禁用PID
void enablePID(PIDController* pid, bool enable) {
  if (enable && !pid->enabled) {
    // 从禁用切换到使能时，重置PID状态
    resetPID(pid);
  }
  pid->enabled = enable;
}

// 设置速度设定值
void setSpeedSetpoint(int motorNum, float rpm) {
  // 限制转速范围
  if (rpm > RPM_MAX) rpm = RPM_MAX;
  else if (rpm < -RPM_MAX) rpm = -RPM_MAX;
  
  if (motorNum == 1) {
    speedPID1.setpoint = rpm;
  } else if (motorNum == 2) {
    speedPID2.setpoint = rpm;
  }
  
  #if ENABLE_SERIAL_DEBUG
  Serial.print("电机");
  Serial.print(motorNum);
  Serial.print("速度设定值: ");
  Serial.print(rpm);
  Serial.println(" RPM");
  #endif
}

// 设置相位设定值
void setPhaseSetpoint(float phase) {
  // 将相位限制在-180到180度之间
  while (phase > 180.0) phase -= 360.0;
  while (phase < -180.0) phase += 360.0;
  
  phasePID.setpoint = phase;
  
  #if ENABLE_SERIAL_DEBUG
  Serial.print("相位设定值: ");
  Serial.print(phase);
  Serial.println("°");
  #endif
}

// 获取速度环输出
float getSpeedOutput(int motorNum) {
  if (motorNum == 1) {
    speedPID1.input = getMotorRPM(1);
    return computePID(&speedPID1);
  } else if (motorNum == 2) {
    speedPID2.input = getMotorRPM(2);
    return computePID(&speedPID2);
  }
  return 0.0;
}

// 获取相位环输出
float getPhaseOutput() {
  phasePID.input = getPhaseError();
  return computePID(&phasePID);
}
