#ifndef ENCODER_H
#define ENCODER_H

#include "config.h"

// 编码器数据结构
struct EncoderData {
  volatile long pulseCount;     // 脉冲计数
  volatile long lastPulseCount; // 上次脉冲计数
  volatile bool direction;      // 旋转方向（true=正转，false=反转）
  float currentRPM;            // 当前转速(RPM)
  float currentAngle;          // 当前角度(度)
  unsigned long lastCalcTime;  // 上次计算时间
  bool isStalled;              // 失步状态
};

// 全局编码器数据
extern EncoderData encoder1;
extern EncoderData encoder2;

// 函数声明
void initEncoders();                    // 初始化编码器
void calculateSpeed();                  // 计算转速
void calculatePhase();                  // 计算相位差
float getMotorRPM(int motorNum);       // 获取电机转速
float getPhaseError();                 // 获取相位误差
void resetEncoders();                  // 重置编码器
bool isMotorStalled(int motorNum);     // 检测电机失步

// 中断服务函数声明
void encoder1_ISR_A();
void encoder1_ISR_B();
void encoder2_ISR_A();
void encoder2_ISR_B();

#endif // ENCODER_H
