/**
 * @file main.cpp
 * @brief 基于时间轮询调度器的OLED测试系统
 * <AUTHOR> System
 * @date 2024
 */

#include <Arduino.h>
#include "oled.h"
#include "scheduler.h"

/**
 * @brief 系统初始化函数
 */
void setup()
{
  // 初始化串口
  Serial.begin(115200);
  delay(2000); // 等待串口稳定

  Serial.println("=== Time Polling Scheduler System ===");
  Serial.println("Version: 1.0 (Scheduler Mode)");
  Serial.println();

  // Initialize OLED
  Serial.println("Initializing OLED display...");
  if (!oled_init())
  {
    Serial.println("OLED init failed");
    Serial.println("Check OLED connections:");
    Serial.println("VCC -> 5V, GND -> GND");
    Serial.println("SDA -> Pin 20, SCL -> Pin 21");
    while (1)
    {
      delay(1000);
    }
  }

  Serial.println("OLED init success");

  // Show splash screen
  oled_show_splash();
  delay(2000);

  // Initialize scheduler
  Serial.println("Initializing scheduler...");
  scheduler_init();

  Serial.println("System startup complete");
  Serial.println("Starting scheduler...");
  Serial.println();
}

/**
 * @brief 主循环函数
 */
void loop()
{
  // 运行调度器
  scheduler_run();

  // 短暂延时，避免CPU占用过高
  delay(1);
}
