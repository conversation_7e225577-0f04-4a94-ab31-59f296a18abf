#ifndef RUN_MODES_H
#define RUN_MODES_H

#include "config.h"
#include "display.h"

// 运行状态枚举
enum RunStatus {
  STATUS_STOPPED,     // 停止状态
  STATUS_STARTING,    // 启动中
  STATUS_RUNNING,     // 运行中
  STATUS_SYNCING,     // 同步中
  STATUS_CHASING,     // 追逐中
  STATUS_ERROR,       // 错误状态
  STATUS_EMERGENCY    // 急停状态
};

// 模式数据结构
struct ModeData {
  RunMode currentMode;        // 当前运行模式
  RunStatus currentStatus;    // 当前运行状态
  float motor1TargetRPM;     // 电机1目标转速
  float motor2TargetRPM;     // 电机2目标转速
  float targetPhase;         // 目标相位差
  unsigned long modeStartTime; // 模式开始时间
  unsigned long syncStartTime; // 同步开始时间
  bool syncAchieved;         // 同步已达成
  bool phaseAchieved;        // 相位已达成
  float maxSpeedError;       // 最大速度误差
  float maxPhaseError;       // 最大相位误差
};

// 全局模式数据
extern ModeData modeData;

// 函数声明
void initRunModes();                        // 初始化运行模式
void updateRunModes();                      // 更新运行模式
void setRunMode(RunMode mode);              // 设置运行模式
void startMode();                           // 启动当前模式
void stopMode();                            // 停止当前模式
void pauseMode();                           // 暂停当前模式
void resumeMode();                          // 恢复当前模式

// 模式处理函数
void processIndependentMode();              // 处理独立调速模式
void processSyncMode();                     // 处理同步运行模式
void processPhaseLockMode();                // 处理相位锁定模式
void processChaseMode();                    // 处理追逐模式
void processManualMode();                   // 处理手动模式

// 状态检查函数
bool isSpeedSynced();                       // 检查速度是否同步
bool isPhaseLocked();                       // 检查相位是否锁定
bool isSystemStable();                      // 检查系统是否稳定
float getSpeedError();                      // 获取速度误差
float getCurrentPhaseError();               // 获取当前相位误差

// 辅助函数
void calculateErrors();                     // 计算误差
void updateModeStatus();                    // 更新模式状态
void handleModeTransition();                // 处理模式切换
void resetModeData();                       // 重置模式数据

#endif // RUN_MODES_H
