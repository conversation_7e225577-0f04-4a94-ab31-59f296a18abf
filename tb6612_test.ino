/*
 * TB6612FNG驱动器测试程序
 * 用于验证TB6612FNG驱动器和电机连接是否正常
 */

// TB6612FNG引脚定义
#define MOTOR1_PWM_PIN    9   // 电机1 PWM
#define MOTOR1_IN1_PIN    7   // 电机1 AIN1
#define MOTOR1_IN2_PIN    8   // 电机1 AIN2

#define MOTOR2_PWM_PIN    12  // 电机2 PWM
#define MOTOR2_IN1_PIN    10  // 电机2 BIN1
#define MOTOR2_IN2_PIN    11  // 电机2 BIN2

#define MOTOR_STBY_PIN    5   // 待机控制引脚

void setup() {
  Serial.begin(115200);
  Serial.println("=== TB6612FNG驱动器测试 ===");
  
  // 初始化引脚
  pinMode(MOTOR1_PWM_PIN, OUTPUT);
  pinMode(MOTOR1_IN1_PIN, OUTPUT);
  pinMode(MOTOR1_IN2_PIN, OUTPUT);
  
  pinMode(MOTOR2_PWM_PIN, OUTPUT);
  pinMode(MOTOR2_IN1_PIN, OUTPUT);
  pinMode(MOTOR2_IN2_PIN, OUTPUT);
  
  pinMode(MOTOR_STBY_PIN, OUTPUT);
  
  // 解除待机模式
  digitalWrite(MOTOR_STBY_PIN, HIGH);
  
  Serial.println("引脚初始化完成");
  Serial.println("开始电机测试...");
}

void loop() {
  // 测试电机1
  Serial.println("测试电机1正转...");
  setMotor(1, 150, true);  // 电机1，PWM=150，正转
  delay(2000);
  
  Serial.println("测试电机1反转...");
  setMotor(1, 150, false); // 电机1，PWM=150，反转
  delay(2000);
  
  Serial.println("停止电机1");
  stopMotor(1);
  delay(1000);
  
  // 测试电机2
  Serial.println("测试电机2正转...");
  setMotor(2, 150, true);  // 电机2，PWM=150，正转
  delay(2000);
  
  Serial.println("测试电机2反转...");
  setMotor(2, 150, false); // 电机2，PWM=150，反转
  delay(2000);
  
  Serial.println("停止电机2");
  stopMotor(2);
  delay(1000);
  
  // 测试双电机同时运行
  Serial.println("测试双电机同时正转...");
  setMotor(1, 120, true);
  setMotor(2, 120, true);
  delay(3000);
  
  Serial.println("测试双电机反向运行...");
  setMotor(1, 120, true);
  setMotor(2, 120, false);
  delay(3000);
  
  Serial.println("停止所有电机");
  stopAllMotors();
  delay(2000);
  
  // 测试待机功能
  Serial.println("测试待机功能...");
  digitalWrite(MOTOR_STBY_PIN, LOW);  // 激活待机
  setMotor(1, 200, true);  // 尝试启动电机（应该无效）
  delay(2000);
  
  digitalWrite(MOTOR_STBY_PIN, HIGH); // 解除待机
  Serial.println("解除待机，电机应该可以运行");
  setMotor(1, 200, true);
  delay(2000);
  
  stopAllMotors();
  delay(3000);
  
  Serial.println("=== 测试循环完成 ===\n");
}

// 设置电机运行
void setMotor(int motorNum, int pwmValue, bool forward) {
  // 限制PWM值范围
  if (pwmValue > 255) pwmValue = 255;
  if (pwmValue < 0) pwmValue = 0;
  
  if (motorNum == 1) {
    // 电机1控制
    if (forward) {
      digitalWrite(MOTOR1_IN1_PIN, HIGH);
      digitalWrite(MOTOR1_IN2_PIN, LOW);
    } else {
      digitalWrite(MOTOR1_IN1_PIN, LOW);
      digitalWrite(MOTOR1_IN2_PIN, HIGH);
    }
    analogWrite(MOTOR1_PWM_PIN, pwmValue);
    
    Serial.print("电机1: PWM=");
    Serial.print(pwmValue);
    Serial.print(", 方向=");
    Serial.println(forward ? "正转" : "反转");
    
  } else if (motorNum == 2) {
    // 电机2控制
    if (forward) {
      digitalWrite(MOTOR2_IN1_PIN, HIGH);
      digitalWrite(MOTOR2_IN2_PIN, LOW);
    } else {
      digitalWrite(MOTOR2_IN1_PIN, LOW);
      digitalWrite(MOTOR2_IN2_PIN, HIGH);
    }
    analogWrite(MOTOR2_PWM_PIN, pwmValue);
    
    Serial.print("电机2: PWM=");
    Serial.print(pwmValue);
    Serial.print(", 方向=");
    Serial.println(forward ? "正转" : "反转");
  }
}

// 停止指定电机
void stopMotor(int motorNum) {
  if (motorNum == 1) {
    digitalWrite(MOTOR1_IN1_PIN, LOW);
    digitalWrite(MOTOR1_IN2_PIN, LOW);
    analogWrite(MOTOR1_PWM_PIN, 0);
    Serial.println("电机1已停止");
  } else if (motorNum == 2) {
    digitalWrite(MOTOR2_IN1_PIN, LOW);
    digitalWrite(MOTOR2_IN2_PIN, LOW);
    analogWrite(MOTOR2_PWM_PIN, 0);
    Serial.println("电机2已停止");
  }
}

// 停止所有电机
void stopAllMotors() {
  stopMotor(1);
  stopMotor(2);
  Serial.println("所有电机已停止");
}

// 刹车模式（短路电机）
void brakeMotor(int motorNum) {
  if (motorNum == 1) {
    digitalWrite(MOTOR1_IN1_PIN, HIGH);
    digitalWrite(MOTOR1_IN2_PIN, HIGH);
    analogWrite(MOTOR1_PWM_PIN, 255);
    Serial.println("电机1刹车");
  } else if (motorNum == 2) {
    digitalWrite(MOTOR2_IN1_PIN, HIGH);
    digitalWrite(MOTOR2_IN2_PIN, HIGH);
    analogWrite(MOTOR2_PWM_PIN, 255);
    Serial.println("电机2刹车");
  }
}

/*
 * TB6612FNG真值表：
 * 
 * IN1 | IN2 | PWM | 输出
 * ----|-----|-----|------
 *  H  |  L  |  H  | 正转
 *  L  |  H  |  H  | 反转
 *  L  |  L  |  H  | 短路刹车
 *  H  |  H  |  H  | 短路刹车
 *  X  |  X  |  L  | 停止
 * 
 * STBY = L: 待机模式（所有输出关闭）
 * STBY = H: 正常工作模式
 */
