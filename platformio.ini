; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:megaatmega2560]
platform = atmelavr
board = megaatmega2560
framework = arduino
upload_port = COM15
upload_speed = 115200
upload_flags = -V -D
build_flags =
    -Os                     # 优化代码大小
    -Wall                   # 启用所有警告
monitor_speed = 115200      # 串口监视器波特率
lib_deps =
    adafruit/Adafruit SSD1306@^2.5.7
    adafruit/Adafruit GFX Library@^1.11.3
    feilipu/FreeRTOS@^11.1.0-3
    olikraus/U8g2@^2.35.9
