# 双电机协同运动控制系统

基于Arduino Mega 2560的双电机协同运动控制系统，采用双闭环PID控制（速度环+相位环），支持多种运行模式和安全保护功能。

## 🎯 项目特性

### 基本功能
- **独立调速控制**：每个电机可独立设定转速（60-300 RPM），控制精度≤±2 RPM
- **同步运行控制**：两个电机保持相同转速稳定旋转，转速误差≤±2 RPM
- **相位锁定控制**：两个电机保持固定相位差（0°、90°、180°、270°），相位误差≤±5°
- **实时显示**：OLED显示屏实时显示转速（精度0.1 RPM）和相位差（精度1°），刷新频率≥2Hz

### 发挥功能
- **可变相位锁定**：动态设定相位差（0°-360°，步进1°），5秒内稳定到新相位
- **追逐模式**：从电机2秒内加速并与主电机实现同速同相
- **动态响应与抗扰**：负载移除后3秒内恢复同步
- **失步检测与报警**：实时监控电机状态，失步时自动报警
- **友好人机界面**：支持按键操作，多级菜单系统

## � 硬件配置

### 主控制器
- **Arduino Mega 2560**：ATmega2560微控制器，54路数字I/O，16路模拟输入

### 电机系统
- **直流电机**：12V额定电压，功率≥10W
- **电机驱动器**：TB6612FNG双H桥驱动模块 x2
- **编码器**：增量式光电编码器，500线/圈 x2

### 显示与交互
- **显示屏**：SSD1306 OLED 128x64像素，I2C接口
- **按钮**：5个按钮（模式、设置、上、下、确认）

### 引脚分配
```
电机1控制 (TB6612FNG1)：
- PWMA: Pin 9
- AIN1: Pin 7
- AIN2: Pin 8

电机2控制 (TB6612FNG2)：
- PWMB: Pin 12
- BIN1: Pin 10
- BIN2: Pin 11

TB6612FNG控制：
- STBY: Pin 5 (待机控制)

编码器：
- 编码器1 A相: Pin 2 (INT0)
- 编码器1 B相: Pin 3 (INT1)
- 编码器2 A相: Pin 18 (INT5)
- 编码器2 B相: Pin 19 (INT4)

显示与交互：
- OLED SDA: Pin 20
- OLED SCL: Pin 21
- 按钮: Pin 26-30

状态指示：
- 状态LED: Pin 13
- 错误LED: Pin 31
```

## 🔌 硬件连接说明

### TB6612FNG驱动器接线
**TB6612FNG1 (电机1)：**
- VM → 12V+
- VCC → 5V
- GND → GND
- STBY → Pin 5
- AIN1 → Pin 7
- AIN2 → Pin 8
- PWMA → Pin 9
- A01 → 电机1红线
- A02 → 电机1白线

**TB6612FNG2 (电机2)：**
- VM → 12V+
- VCC → 5V
- GND → GND
- STBY → Pin 5 (与TB6612FNG1共用)
- BIN1 → Pin 10
- BIN2 → Pin 11
- PWMB → Pin 12
- B01 → 电机2红线
- B02 → 电机2白线

### 其他连接
- 编码器按照引脚分配表连接
- OLED显示屏：I2C (SDA/SCL)
- 按钮连接到指定引脚
- 电源：12V直流供电

## 🛠️ 编译和上传

### 环境要求
- PlatformIO IDE
- Arduino Mega 2560开发板
- FreeRTOS库 (自动安装)
- Adafruit SSD1306库 (自动安装)

### 编译步骤
```bash
# 编译项目
pio run

# 上传到开发板
pio run --target upload

# 串口监视器
pio device monitor
```

## 📱 用户界面操作

### 主界面显示
- 电机A/B实时转速 (RPM)
- 相位差显示 (度)
- 同步状态指示
- 相位锁定状态

### 按键功能
- **按键1**：进入菜单
- **按键2**：启动/停止电机
- **按键3**：查看系统状态
- **旋转编码器**：调整参数值

### 菜单系统
1. **Set Speed** - 设置目标转速
2. **Set Phase** - 设置目标相位差
3. **PID Tuning** - 调整PID参数
4. **System Info** - 查看系统信息
5. **Back to Main** - 返回主界面

## ⚙️ 系统配置

### 关键参数 (project_config.h)
```cpp
#define MAX_RPM 3000                // 最大转速
#define ENCODER_PPR 1000           // 编码器分辨率
#define SYNC_TOLERANCE_RPM 5.0f    // 同步容差
#define PHASE_TOLERANCE_DEG 2.0f   // 相位容差

// PID默认参数
#define PID_KP_DEFAULT 1.0f
#define PID_KI_DEFAULT 0.1f  
#define PID_KD_DEFAULT 0.05f
```

### 运行模式
- **MOTOR_MODE_STOP** - 停止模式
- **MOTOR_MODE_SPEED** - 速度控制模式
- **MOTOR_MODE_SYNC** - 同步模式
- **MOTOR_MODE_PHASE_LOCK** - 相位锁定模式

## 🔍 调试和监控

### 串口输出
系统通过串口(115200波特率)输出详细的调试信息：
- 系统启动信息
- 任务运行状态
- 电机转速和相位数据
- 错误和报警信息

### 状态监控
- 实时转速显示
- 同步状态指示
- 内存使用情况
- 任务栈使用情况

## ⚠️ 安全特性

### 报警系统
- **ALARM_LOST_STEP** - 失步检测
- **ALARM_OVERSPEED** - 超速保护
- **ALARM_ENCODER_ERROR** - 编码器故障
- **ALARM_SYNC_LOST** - 同步丢失
- **ALARM_CRITICAL** - 严重错误

### 故障保护
- 栈溢出检测
- 内存分配失败处理
- 看门狗保护
- 紧急停机功能

## 📊 性能指标

- **控制精度**：±0.1 RPM
- **同步精度**：±5 RPM
- **相位精度**：±2°
- **响应时间**：<10ms
- **内存使用**：~1.2KB RAM, ~85KB Flash

## 🔧 故障排除

### 常见问题
1. **编译错误**：检查库依赖是否正确安装
2. **电机不转**：检查PWM和方向引脚连接
3. **编码器无信号**：检查中断引脚连接和上拉电阻
4. **同步失败**：调整PID参数和同步容差
5. **显示异常**：检查I2C连接和地址设置

### 调试技巧
- 使用串口监视器查看系统状态
- 检查任务栈使用情况
- 监控内存使用情况
- 观察报警标志位

## 📝 开发说明

### 添加新功能
1. 在相应的模块头文件中声明函数
2. 在实现文件中编写功能代码
3. 更新数据结构和通信接口
4. 测试并验证功能

### 代码规范
- 使用中文注释说明功能
- 函数名采用下划线命名法
- 全局变量以g_前缀标识
- 常量使用大写字母定义

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 👥 贡献

欢迎提交Issue和Pull Request来改进本项目。

---

**版本**: 1.0.0  
**作者**: FreeRTOS Motor Control Team  
**更新日期**: 2024年
