#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

#include "config.h"

// PID控制器结构体
struct PIDController {
  float kp;           // 比例系数
  float ki;           // 积分系数  
  float kd;           // 微分系数
  float setpoint;     // 设定值
  float input;        // 输入值（反馈值）
  float output;       // 输出值
  float lastInput;    // 上次输入值
  float integral;     // 积分累积
  float lastError;    // 上次误差
  float outputMin;    // 输出最小值
  float outputMax;    // 输出最大值
  unsigned long lastTime; // 上次计算时间
  bool enabled;       // 使能标志
};

// 全局PID控制器实例
extern PIDController speedPID1;    // 电机1速度环PID
extern PIDController speedPID2;    // 电机2速度环PID
extern PIDController phasePID;     // 相位环PID

// 函数声明
void initPIDControllers();                    // 初始化PID控制器
float computePID(PIDController* pid);         // 计算PID输出
void setPIDTunings(PIDController* pid, float kp, float ki, float kd); // 设置PID参数
void setPIDLimits(PIDController* pid, float min, float max);          // 设置输出限制
void resetPID(PIDController* pid);            // 重置PID控制器
void enablePID(PIDController* pid, bool enable); // 使能/禁用PID
void setSpeedSetpoint(int motorNum, float rpm);  // 设置速度设定值
void setPhaseSetpoint(float phase);              // 设置相位设定值
float getSpeedOutput(int motorNum);              // 获取速度环输出
float getPhaseOutput();                          // 获取相位环输出

#endif // PID_CONTROLLER_H
