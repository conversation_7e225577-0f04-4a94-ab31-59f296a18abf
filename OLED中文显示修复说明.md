# OLED中文显示修复说明

## 问题描述
原项目中OLED显示中文字符时出现乱码，这是因为使用的Adafruit_SSD1306库只支持ASCII字符，不支持中文字符显示。

## 解决方案
将OLED显示库从Adafruit_SSD1306切换到U8g2库，U8g2库内置了中文字体支持。

## 修改内容

### 1. 库依赖更新 (platformio.ini)
```ini
# 原来的依赖
adafruit/Adafruit SSD1306@^2.5.7
adafruit/Adafruit GFX Library@^1.11.3

# 修改为
olikraus/U8g2@^2.35.9
```

### 2. 头文件修改 (src/oled.h)
```cpp
// 原来的包含
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

// 修改为
#include <U8g2lib.h>
```

### 3. 显示对象创建 (src/oled.cpp)
```cpp
// 原来的对象
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

// 修改为
U8G2_SSD1306_128X64_NONAME_F_HW_I2C u8g2(U8G2_R0, /* reset=*/ U8X8_PIN_NONE);
```

### 4. 初始化函数修改
```cpp
// 原来的初始化
display.begin(SSD1306_SWITCHCAPVCC, OLED_ADDRESS)
display.setTextColor(SSD1306_WHITE);
display.setTextSize(1);

// 修改为
u8g2.begin()
u8g2.setFont(u8g2_font_wqy12_t_chinese1);  // 设置中文字体
```

### 5. 显示函数修改
```cpp
// 原来的显示方式
display.clearDisplay();
display.setCursor(x, y);
display.print("文本");
display.display();

// 修改为
u8g2.clearBuffer();
u8g2.setCursor(x, y);
u8g2.print("文本");
u8g2.sendBuffer();
```

### 6. 图形绘制函数修改
```cpp
// 原来的绘制
display.drawRect(x, y, w, h, SSD1306_WHITE);
display.fillRect(x, y, w, h, SSD1306_WHITE);

// 修改为
u8g2.drawFrame(x, y, w, h);
u8g2.drawBox(x, y, w, h);
```

## 使用的中文字体
- **u8g2_font_wqy12_t_chinese1**: 12像素中文字体，适合正常显示
- **u8g2_font_wqy16_t_chinese1**: 16像素中文字体，适合标题显示

## 修改的文件列表
1. `src/oled.h` - 头文件更新
2. `src/oled.cpp` - 显示模块实现更新
3. `simple_oled_test/simple_oled_test.ino` - 测试程序更新
4. `platformio.ini` - 项目配置文件（新建）
5. `README.md` - 文档更新

## 功能特性
- ✅ 完美支持中文字符显示
- ✅ 支持中英文混合显示
- ✅ 保持原有功能不变
- ✅ 显示效果更清晰
- ✅ 内存使用优化

## 测试验证
编译成功，无错误警告。项目现在可以正确显示中文字符，解决了乱码问题。

## 注意事项
1. U8g2库比Adafruit库稍大，但提供了更好的中文支持
2. 显示函数调用方式有所变化，但功能保持一致
3. 字体文件已内置在U8g2库中，无需额外添加
4. 支持多种中文字体大小选择

## 兼容性
- ✅ Arduino Mega 2560
- ✅ SSD1306 OLED显示屏
- ✅ I2C通信协议
- ✅ PlatformIO开发环境
