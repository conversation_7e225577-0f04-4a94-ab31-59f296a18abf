#include "encoder.h"
#include <Arduino.h>

// 全局编码器数据实例
EncoderData encoder1 = {0, 0, true, 0.0, 0.0, 0, false};
EncoderData encoder2 = {0, 0, true, 0.0, 0.0, 0, false};

// 相位差变量
float currentPhaseError = 0.0;
float targetPhaseError = 0.0;

// 初始化编码器
void initEncoders() {
  // 设置编码器引脚为输入模式，启用内部上拉电阻
  pinMode(ENCODER1_A_PIN, INPUT_PULLUP);
  pinMode(ENCODER1_B_PIN, INPUT_PULLUP);
  pinMode(ENCODER2_A_PIN, INPUT_PULLUP);
  pinMode(ENCODER2_B_PIN, INPUT_PULLUP);
  
  // 绑定中断服务函数
  attachInterrupt(digitalPinToInterrupt(ENCODER1_A_PIN), encoder1_ISR_A, CHANGE);
  attachInterrupt(digitalPinToInterrupt(ENCODER1_B_PIN), encoder1_ISR_B, CHANGE);
  attachInterrupt(digitalPinToInterrupt(ENCODER2_A_PIN), encoder2_ISR_A, CHANGE);
  attachInterrupt(digitalPinToInterrupt(ENCODER2_B_PIN), encoder2_ISR_B, CHANGE);
  
  // 初始化时间戳
  encoder1.lastCalcTime = millis();
  encoder2.lastCalcTime = millis();
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("编码器初始化完成");
  #endif
}

// 编码器1 A相中断服务函数
void encoder1_ISR_A() {
  bool A = digitalRead(ENCODER1_A_PIN);
  bool B = digitalRead(ENCODER1_B_PIN);
  
  if (A == B) {
    encoder1.pulseCount++;
    encoder1.direction = true;  // 正转
  } else {
    encoder1.pulseCount--;
    encoder1.direction = false; // 反转
  }
}

// 编码器1 B相中断服务函数
void encoder1_ISR_B() {
  bool A = digitalRead(ENCODER1_A_PIN);
  bool B = digitalRead(ENCODER1_B_PIN);
  
  if (A != B) {
    encoder1.pulseCount++;
    encoder1.direction = true;  // 正转
  } else {
    encoder1.pulseCount--;
    encoder1.direction = false; // 反转
  }
}

// 编码器2 A相中断服务函数
void encoder2_ISR_A() {
  bool A = digitalRead(ENCODER2_A_PIN);
  bool B = digitalRead(ENCODER2_B_PIN);
  
  if (A == B) {
    encoder2.pulseCount++;
    encoder2.direction = true;  // 正转
  } else {
    encoder2.pulseCount--;
    encoder2.direction = false; // 反转
  }
}

// 编码器2 B相中断服务函数
void encoder2_ISR_B() {
  bool A = digitalRead(ENCODER2_A_PIN);
  bool B = digitalRead(ENCODER2_B_PIN);
  
  if (A != B) {
    encoder2.pulseCount++;
    encoder2.direction = true;  // 正转
  } else {
    encoder2.pulseCount--;
    encoder2.direction = false; // 反转
  }
}

// 计算电机转速
void calculateSpeed() {
  unsigned long currentTime = millis();
  
  // 计算电机1转速
  if (currentTime - encoder1.lastCalcTime >= SPEED_CALC_PERIOD) {
    long pulseDiff = encoder1.pulseCount - encoder1.lastPulseCount;
    float timeDiff = (currentTime - encoder1.lastCalcTime) / 1000.0; // 转换为秒
    
    // RPM = (脉冲差 / 编码器每转计数) * (60秒/分钟) / 时间差
    encoder1.currentRPM = (pulseDiff / (float)ENCODER_CPR) * (60.0 / timeDiff);
    encoder1.currentAngle = (encoder1.pulseCount % ENCODER_CPR) * (360.0 / ENCODER_CPR);
    
    encoder1.lastPulseCount = encoder1.pulseCount;
    encoder1.lastCalcTime = currentTime;
    
    // 失步检测
    encoder1.isStalled = (abs(encoder1.currentRPM) < 1.0 && abs(pulseDiff) < 5);
  }
  
  // 计算电机2转速
  if (currentTime - encoder2.lastCalcTime >= SPEED_CALC_PERIOD) {
    long pulseDiff = encoder2.pulseCount - encoder2.lastPulseCount;
    float timeDiff = (currentTime - encoder2.lastCalcTime) / 1000.0; // 转换为秒
    
    // RPM = (脉冲差 / 编码器每转计数) * (60秒/分钟) / 时间差
    encoder2.currentRPM = (pulseDiff / (float)ENCODER_CPR) * (60.0 / timeDiff);
    encoder2.currentAngle = (encoder2.pulseCount % ENCODER_CPR) * (360.0 / ENCODER_CPR);
    
    encoder2.lastPulseCount = encoder2.pulseCount;
    encoder2.lastCalcTime = currentTime;
    
    // 失步检测
    encoder2.isStalled = (abs(encoder2.currentRPM) < 1.0 && abs(pulseDiff) < 5);
  }
}

// 计算相位差
void calculatePhase() {
  // 计算两个电机的相位差（度）
  float angle1 = encoder1.currentAngle;
  float angle2 = encoder2.currentAngle;
  
  currentPhaseError = angle2 - angle1;
  
  // 将相位差限制在-180到180度之间
  while (currentPhaseError > 180.0) currentPhaseError -= 360.0;
  while (currentPhaseError < -180.0) currentPhaseError += 360.0;
}

// 获取电机转速
float getMotorRPM(int motorNum) {
  if (motorNum == 1) return encoder1.currentRPM;
  else if (motorNum == 2) return encoder2.currentRPM;
  return 0.0;
}

// 获取相位误差
float getPhaseError() {
  return currentPhaseError;
}

// 重置编码器
void resetEncoders() {
  noInterrupts(); // 禁用中断
  
  encoder1.pulseCount = 0;
  encoder1.lastPulseCount = 0;
  encoder1.currentRPM = 0.0;
  encoder1.currentAngle = 0.0;
  encoder1.isStalled = false;
  
  encoder2.pulseCount = 0;
  encoder2.lastPulseCount = 0;
  encoder2.currentRPM = 0.0;
  encoder2.currentAngle = 0.0;
  encoder2.isStalled = false;
  
  currentPhaseError = 0.0;
  
  interrupts(); // 重新启用中断
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("编码器已重置");
  #endif
}

// 检测电机失步
bool isMotorStalled(int motorNum) {
  if (motorNum == 1) return encoder1.isStalled;
  else if (motorNum == 2) return encoder2.isStalled;
  return false;
}
