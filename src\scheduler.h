/**
 * @file scheduler.h
 * @brief 简单的时间轮询调度器
 */

#ifndef SCHEDULER_H
#define SCHEDULER_H

#include <Arduino.h>

// 任务函数类型定义
typedef void (*task_func_t)(void);

// 任务结构体
typedef struct {
    task_func_t task_func;    // 任务函数指针
    uint32_t rate_ms;         // 执行周期(毫秒)
    uint32_t last_run;        // 上次执行时间
    bool enabled;             // 任务是否启用
} task_t;

// 调度器函数
void scheduler_init(void);
void scheduler_run(void);
void scheduler_add_task(task_func_t func, uint32_t rate_ms);
void scheduler_enable_task(uint8_t task_id, bool enable);

// 任务函数声明
void oled_task(void);
void uart_task(void);

#endif // SCHEDULER_H
