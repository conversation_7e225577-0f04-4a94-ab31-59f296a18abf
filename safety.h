#ifndef SAFETY_H
#define SAFETY_H

#include "config.h"

// 错误代码枚举
enum ErrorCode {
  ERROR_NONE,                // 无错误
  ERROR_MOTOR1_STALL,        // 电机1失步
  ERROR_MOTOR2_STALL,        // 电机2失步
  ERROR_SYNC_TIMEOUT,        // 同步超时
  ERROR_PHASE_LOCK_TIMEOUT,  // 相位锁定超时
  ERROR_MOTOR1_OVERLOAD,     // 电机1过载
  ERROR_MOTOR2_OVERLOAD,     // 电机2过载
  ERROR_EMERGENCY_STOP,      // 急停激活
  ERROR_COMMUNICATION,       // 通信错误
  ERROR_ENCODER1_FAILURE,    // 编码器1故障
  ERROR_ENCODER2_FAILURE     // 编码器2故障
};

// 安全状态结构体
struct SafetyStatus {
  bool emergencyStop;        // 急停状态
  bool motor1Stalled;        // 电机1失步
  bool motor2Stalled;        // 电机2失步
  bool motor1Overload;       // 电机1过载
  bool motor2Overload;       // 电机2过载
  bool encoder1Failure;      // 编码器1故障
  bool encoder2Failure;      // 编码器2故障
  ErrorCode lastError;       // 最后一个错误
  unsigned long errorTime;   // 错误发生时间
  int errorCount;            // 错误计数
  bool alarmActive;          // 报警激活
};

// 全局安全状态
extern SafetyStatus safetyStatus;

// 函数声明
void initSafety();                           // 初始化安全系统
void updateSafety();                         // 更新安全状态
bool checkMotorStall(int motorNum);          // 检查电机失步
bool checkMotorOverload(int motorNum);       // 检查电机过载
bool checkEncoderFailure(int encoderNum);    // 检查编码器故障
void activateEmergencyStop();                // 激活急停
void deactivateEmergencyStop();              // 解除急停
void handleError(ErrorCode error);           // 处理错误
void resetErrors();                          // 重置错误
void activateAlarm();                        // 激活报警
void deactivateAlarm();                      // 解除报警
const char* getErrorMessage(ErrorCode error); // 获取错误信息

#endif // SAFETY_H
