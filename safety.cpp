#include "safety.h"
#include "encoder.h"
#include "motor_control.h"
#include "display.h"
#include <Arduino.h>

// 全局安全状态实例
SafetyStatus safetyStatus;

// 失步检测参数
const float STALL_RPM_THRESHOLD = 5.0;      // 失步转速阈值
const unsigned long STALL_TIME_THRESHOLD = 1000; // 失步时间阈值(ms)

// 过载检测参数
const float OVERLOAD_CURRENT_THRESHOLD = 2.0; // 过载电流阈值(A)
const unsigned long OVERLOAD_TIME_THRESHOLD = 500; // 过载时间阈值(ms)

// 编码器故障检测参数
const unsigned long ENCODER_TIMEOUT = 2000;   // 编码器超时时间(ms)

// 内部变量
unsigned long motor1StallStartTime = 0;
unsigned long motor2StallStartTime = 0;
unsigned long motor1OverloadStartTime = 0;
unsigned long motor2OverloadStartTime = 0;
unsigned long encoder1LastUpdateTime = 0;
unsigned long encoder2LastUpdateTime = 0;

// 初始化安全系统
void initSafety() {
  // 初始化安全状态
  safetyStatus.emergencyStop = false;
  safetyStatus.motor1Stalled = false;
  safetyStatus.motor2Stalled = false;
  safetyStatus.motor1Overload = false;
  safetyStatus.motor2Overload = false;
  safetyStatus.encoder1Failure = false;
  safetyStatus.encoder2Failure = false;
  safetyStatus.lastError = ERROR_NONE;
  safetyStatus.errorTime = 0;
  safetyStatus.errorCount = 0;
  safetyStatus.alarmActive = false;
  
  // 初始化时间戳
  encoder1LastUpdateTime = millis();
  encoder2LastUpdateTime = millis();
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("安全系统初始化完成");
  #endif
}

// 更新安全状态
void updateSafety() {
  unsigned long currentTime = millis();
  
  // 检查电机失步
  bool motor1Stall = checkMotorStall(1);
  bool motor2Stall = checkMotorStall(2);
  
  // 检查电机过载
  bool motor1Overload = checkMotorOverload(1);
  bool motor2Overload = checkMotorOverload(2);
  
  // 检查编码器故障
  bool encoder1Failure = checkEncoderFailure(1);
  bool encoder2Failure = checkEncoderFailure(2);
  
  // 更新安全状态
  if (motor1Stall && !safetyStatus.motor1Stalled) {
    safetyStatus.motor1Stalled = true;
    handleError(ERROR_MOTOR1_STALL);
  } else if (!motor1Stall && safetyStatus.motor1Stalled) {
    safetyStatus.motor1Stalled = false;
  }
  
  if (motor2Stall && !safetyStatus.motor2Stalled) {
    safetyStatus.motor2Stalled = true;
    handleError(ERROR_MOTOR2_STALL);
  } else if (!motor2Stall && safetyStatus.motor2Stalled) {
    safetyStatus.motor2Stalled = false;
  }
  
  if (motor1Overload && !safetyStatus.motor1Overload) {
    safetyStatus.motor1Overload = true;
    handleError(ERROR_MOTOR1_OVERLOAD);
  } else if (!motor1Overload && safetyStatus.motor1Overload) {
    safetyStatus.motor1Overload = false;
  }
  
  if (motor2Overload && !safetyStatus.motor2Overload) {
    safetyStatus.motor2Overload = true;
    handleError(ERROR_MOTOR2_OVERLOAD);
  } else if (!motor2Overload && safetyStatus.motor2Overload) {
    safetyStatus.motor2Overload = false;
  }
  
  if (encoder1Failure && !safetyStatus.encoder1Failure) {
    safetyStatus.encoder1Failure = true;
    handleError(ERROR_ENCODER1_FAILURE);
  } else if (!encoder1Failure && safetyStatus.encoder1Failure) {
    safetyStatus.encoder1Failure = false;
  }
  
  if (encoder2Failure && !safetyStatus.encoder2Failure) {
    safetyStatus.encoder2Failure = true;
    handleError(ERROR_ENCODER2_FAILURE);
  } else if (!encoder2Failure && safetyStatus.encoder2Failure) {
    safetyStatus.encoder2Failure = false;
  }
  
  // 如果有任何错误，激活报警
  if (safetyStatus.motor1Stalled || safetyStatus.motor2Stalled ||
      safetyStatus.motor1Overload || safetyStatus.motor2Overload ||
      safetyStatus.encoder1Failure || safetyStatus.encoder2Failure ||
      safetyStatus.emergencyStop) {
    if (!safetyStatus.alarmActive) {
      activateAlarm();
    }
  } else {
    if (safetyStatus.alarmActive) {
      deactivateAlarm();
    }
  }
}

// 检查电机失步
bool checkMotorStall(int motorNum) {
  unsigned long currentTime = millis();
  float currentRPM = getMotorRPM(motorNum);
  float targetRPM = getMotorTargetRPM(motorNum);
  
  // 如果目标转速为0，不检查失步
  if (abs(targetRPM) < 1.0) {
    if (motorNum == 1) motor1StallStartTime = 0;
    else motor2StallStartTime = 0;
    return false;
  }
  
  // 检查转速是否低于阈值
  bool isStalling = (abs(currentRPM) < STALL_RPM_THRESHOLD) && (abs(targetRPM) > STALL_RPM_THRESHOLD);
  
  if (isStalling) {
    if (motorNum == 1) {
      if (motor1StallStartTime == 0) {
        motor1StallStartTime = currentTime;
      } else if (currentTime - motor1StallStartTime > STALL_TIME_THRESHOLD) {
        return true;
      }
    } else {
      if (motor2StallStartTime == 0) {
        motor2StallStartTime = currentTime;
      } else if (currentTime - motor2StallStartTime > STALL_TIME_THRESHOLD) {
        return true;
      }
    }
  } else {
    // 重置失步计时器
    if (motorNum == 1) motor1StallStartTime = 0;
    else motor2StallStartTime = 0;
  }
  
  return false;
}

// 检查电机过载
bool checkMotorOverload(int motorNum) {
  // 这里应该读取电机电流，但Arduino Mega没有内置电流检测
  // 可以通过外部电流传感器实现，这里仅作为示例
  
  // 简化的过载检测：基于PWM值和转速差异
  float currentRPM = getMotorRPM(motorNum);
  float targetRPM = getMotorTargetRPM(motorNum);
  float rpmError = abs(targetRPM - currentRPM);
  
  // 如果转速误差持续过大，可能是过载
  bool isOverloading = (rpmError > RPM_ERROR_MAX * 3) && (abs(targetRPM) > 10.0);
  
  unsigned long currentTime = millis();
  
  if (isOverloading) {
    if (motorNum == 1) {
      if (motor1OverloadStartTime == 0) {
        motor1OverloadStartTime = currentTime;
      } else if (currentTime - motor1OverloadStartTime > OVERLOAD_TIME_THRESHOLD) {
        return true;
      }
    } else {
      if (motor2OverloadStartTime == 0) {
        motor2OverloadStartTime = currentTime;
      } else if (currentTime - motor2OverloadStartTime > OVERLOAD_TIME_THRESHOLD) {
        return true;
      }
    }
  } else {
    // 重置过载计时器
    if (motorNum == 1) motor1OverloadStartTime = 0;
    else motor2OverloadStartTime = 0;
  }
  
  return false;
}

// 检查编码器故障
bool checkEncoderFailure(int encoderNum) {
  unsigned long currentTime = millis();
  
  // 检查编码器是否长时间没有更新
  if (encoderNum == 1) {
    // 检查编码器1的脉冲计数是否有变化
    static long lastPulseCount1 = 0;
    if (encoder1.pulseCount != lastPulseCount1) {
      encoder1LastUpdateTime = currentTime;
      lastPulseCount1 = encoder1.pulseCount;
    }
    return (currentTime - encoder1LastUpdateTime > ENCODER_TIMEOUT) && (abs(getMotorTargetRPM(1)) > 10.0);
  } else {
    // 检查编码器2的脉冲计数是否有变化
    static long lastPulseCount2 = 0;
    if (encoder2.pulseCount != lastPulseCount2) {
      encoder2LastUpdateTime = currentTime;
      lastPulseCount2 = encoder2.pulseCount;
    }
    return (currentTime - encoder2LastUpdateTime > ENCODER_TIMEOUT) && (abs(getMotorTargetRPM(2)) > 10.0);
  }
}

// 激活急停
void activateEmergencyStop() {
  if (!safetyStatus.emergencyStop) {
    safetyStatus.emergencyStop = true;
    handleError(ERROR_EMERGENCY_STOP);
    emergencyStop(); // 调用电机控制模块的急停函数
    
    #if ENABLE_SERIAL_DEBUG
    Serial.println("急停激活!");
    #endif
  }
}

// 解除急停
void deactivateEmergencyStop() {
  if (safetyStatus.emergencyStop) {
    safetyStatus.emergencyStop = false;
    releaseEmergencyStop(); // 调用电机控制模块的解除急停函数
    
    #if ENABLE_SERIAL_DEBUG
    Serial.println("急停解除");
    #endif
  }
}

// 处理错误
void handleError(ErrorCode error) {
  safetyStatus.lastError = error;
  safetyStatus.errorTime = millis();
  safetyStatus.errorCount++;
  
  #if ENABLE_SERIAL_DEBUG
  Serial.print("安全错误: ");
  Serial.println(getErrorMessage(error));
  #endif
  
  // 根据错误类型采取相应措施
  switch (error) {
    case ERROR_MOTOR1_STALL:
    case ERROR_MOTOR2_STALL:
    case ERROR_MOTOR1_OVERLOAD:
    case ERROR_MOTOR2_OVERLOAD:
      // 电机相关错误，停止相应电机
      if (error == ERROR_MOTOR1_STALL || error == ERROR_MOTOR1_OVERLOAD) {
        stopMotor(1);
      } else {
        stopMotor(2);
      }
      break;
    case ERROR_ENCODER1_FAILURE:
    case ERROR_ENCODER2_FAILURE:
      // 编码器故障，停止所有电机
      stopAllMotors();
      break;
    case ERROR_EMERGENCY_STOP:
      // 急停，立即停止所有电机
      emergencyStop();
      break;
    default:
      break;
  }
  
  // 显示错误信息
  showErrorMessage(getErrorMessage(error));
}

// 重置错误
void resetErrors() {
  safetyStatus.motor1Stalled = false;
  safetyStatus.motor2Stalled = false;
  safetyStatus.motor1Overload = false;
  safetyStatus.motor2Overload = false;
  safetyStatus.encoder1Failure = false;
  safetyStatus.encoder2Failure = false;
  safetyStatus.lastError = ERROR_NONE;
  safetyStatus.errorCount = 0;
  
  // 重置计时器
  motor1StallStartTime = 0;
  motor2StallStartTime = 0;
  motor1OverloadStartTime = 0;
  motor2OverloadStartTime = 0;
  encoder1LastUpdateTime = millis();
  encoder2LastUpdateTime = millis();
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("错误状态已重置");
  #endif
}

// 激活报警
void activateAlarm() {
  safetyStatus.alarmActive = true;
  
  // 点亮错误LED
  digitalWrite(LED_ERROR_PIN, HIGH);
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("报警激活");
  #endif
}

// 解除报警
void deactivateAlarm() {
  safetyStatus.alarmActive = false;
  
  // 熄灭错误LED
  digitalWrite(LED_ERROR_PIN, LOW);
  
  #if ENABLE_SERIAL_DEBUG
  Serial.println("报警解除");
  #endif
}

// 获取错误信息
const char* getErrorMessage(ErrorCode error) {
  switch (error) {
    case ERROR_NONE: return "无错误";
    case ERROR_MOTOR1_STALL: return "电机1失步";
    case ERROR_MOTOR2_STALL: return "电机2失步";
    case ERROR_SYNC_TIMEOUT: return "同步超时";
    case ERROR_PHASE_LOCK_TIMEOUT: return "相位锁定超时";
    case ERROR_MOTOR1_OVERLOAD: return "电机1过载";
    case ERROR_MOTOR2_OVERLOAD: return "电机2过载";
    case ERROR_EMERGENCY_STOP: return "急停激活";
    case ERROR_COMMUNICATION: return "通信错误";
    case ERROR_ENCODER1_FAILURE: return "编码器1故障";
    case ERROR_ENCODER2_FAILURE: return "编码器2故障";
    default: return "未知错误";
  }
}
