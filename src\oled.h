/**
 * @file oled.h
 * @brief OLED显示模块头文件
 */

#ifndef OLED_H
#define OLED_H

#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

// OLED显示屏参数
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET -1
#define OLED_ADDRESS 0x3C

// 初始化OLED显示
bool oled_init(void);

// 显示启动画面
void oled_show_splash(void);

// 显示系统状态
void oled_show_status(uint32_t counter, uint32_t runtime);

#endif // OLED_H
